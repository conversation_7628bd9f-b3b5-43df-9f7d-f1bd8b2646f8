{"name": "react-flux", "version": "0.1.0", "private": true, "engines": {"node": "14.0.0"}, "dependencies": {"@fullcalendar/daygrid": "^5.11.0", "@fullcalendar/interaction": "^5.11.0", "@fullcalendar/react": "^5.11.0", "@fullcalendar/timegrid": "^5.11.0", "@microsoft/signalr": "^6.0.5", "@react-google-maps/api": "^2.7.0", "@testing-library/jest-dom": "^5.11.9", "@testing-library/react": "^11.2.5", "@testing-library/user-event": "^12.8.1", "async-dispatch": "^1.0.10", "crypto-js": "^4.1.1", "express": "^4.17.3", "express-static-gzip": "^2.1.1", "highcharts": "^9.2.2", "highcharts-react-official": "^3.0.0", "intro.js": "^4.2.2", "intro.js-react": "^0.5.0", "node-sass": "^6.0.0", "prerender-node": "^3.5.0", "re": "^0.1.4", "react": "^17.0.1", "react-card-flip": "^1.1.3", "react-device-detect": "^2.2.2", "react-dom": "^17.0.1", "react-google-maps": "^9.4.5", "react-helmet": "^6.1.0", "react-modal": "^3.14.3", "react-rating-stars-component": "^2.2.0", "react-redux": "^7.2.2", "react-responsive-carousel": "^3.2.21", "react-router-dom": "^5.2.0", "react-scripts": "4.0.3", "react-share": "^4.4.0", "react-toastify": "^8.0.2", "recompose": "^0.30.0", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.9", "redux-logger": "^3.0.6", "redux-persist": "^6.0.0", "redux-thunk": "^2.3.0", "showdown": "^1.9.1", "swiper": "^6.8.3", "ua-parser-js": "0.7.32", "uuidv4": "^6.2.12", "web-vitals": "^1.1.0"}, "scripts": {"start": "GENERATE_SOURCEMAP=false node ./bin/www", "start:dev": "GENERATE_SOURCEMAP=false react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}}