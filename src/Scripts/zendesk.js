import { getWebSocketUrl } from "../Apis/zendesk.api";
const REQUEST_ID = {
  MESSAGE_SUBSCRIPTION: 1,
  UPDATE_AGENT_STATUS: 2,
  SEND_MESSAGE: 3,
  GET_DEPARTMENTS: 4,
  TRANSFER_TO_DEPARTMENT: 5,
  SEND_QUICK_REPLIES: 6,
};

// const ACCESS_TOKEN =
//   "hczAA8hVYXuejgTLaHkaPRAAzNPiF4HwvkVqaK5vIJ4zuEw1NK1bHXjxpccOmdix";

const CHAT_API_URL = "https://chat-api.zopim.com/graphql/request";

const SUBSCRIPTION_DATA_SIGNAL = "DATA";
const TYPE = {
  VISITOR: "Visitor",
  AGENT: "Agent",
};

const connectToZendesk = (setIncomingActivity, bot_id) => {
  const getWebSoket = getWebSocketUrl(bot_id);
  const websocket_url = getWebSoket.startAgentSession.websocket_url;
  let webSocket = new WebSocket(websocket_url);
  let cb = setIncomingActivity;

  webSocket.onopen = () => {
    let pingInterval;
    console.log(`[WebSocket] Successfully connected to ${websocket_url}`);

    /*************************************************
     * Periodic ping to prevent WebSocket connection *
     * time out due to idle connection               *
     *************************************************/
    pingInterval = setInterval(() => {
      webSocket.send(
        JSON.stringify({
          sig: "PING",
          payload: +new Date(),
        })
      );
    }, 5000);

    /***********************
     * Update agent status *
     ***********************/
    const updateAgentStatusQuery = {
      payload: {
        query: `mutation {
                      updateAgentStatus(status: ONLINE) {
                          node {
                              id
                          }
                      }
                  }`,
      },
      type: "request",
      id: REQUEST_ID.UPDATE_AGENT_STATUS,
    };
    webSocket.send(JSON.stringify(updateAgentStatusQuery));
    console.log("[updateAgentStatus] Request sent");

    /************************
     * Message subscription *
     ************************/
    const messageSubscriptionQuery = {
      payload: {
        query: `subscription {
                      message {
                          node {
                              id
                              content
                              channel {
                                  id
                              }
                              from {
                                  __typename
                                  display_name
                              }
                          }
                      }
                  }`,
      },
      type: "request",
      id: REQUEST_ID.MESSAGE_SUBSCRIPTION,
    };
    webSocket.send(JSON.stringify(messageSubscriptionQuery));
    console.log("[message] Subscription request sent");
  };

  const handleMessage = (message) => {
    const data = JSON.parse(message);

    console.log("data", data);

    // Listen to chat messages from the visitor
    if (data.sig === SUBSCRIPTION_DATA_SIGNAL && data.payload.data) {
      const chatMessage = data.payload.data.message.node;
      const sender = chatMessage.from;

      console.log("sender", sender);

      if (sender.__typename === TYPE.AGENT) {
        cb({
          from: "bot",
          type: "message",
          text: chatMessage.content,
        });
      }
    }
  };

  webSocket.onmessage = (event) => handleMessage(event.data);
};

export { connectToZendesk };
