const IsJsonString = (str) => {
  try {
    JSON.parse(str);
  } catch (e) {
    return false;
  }
  return true;
};
export default function incomingActivityHandler (cb, showMuteIcon, hideMuteIcon, tts, tts_gender ) {
    window.addEventListener('incomingActivity', ({ data }) => { 
          if (data.text && IsJsonString(data.text)) {
            const activity = JSON.parse(data.text);
            console.log("activity",activity)
          
            if(activity?.from === 'bot'){    
              if(activity?.type === 'message'){
               
                if(tts){
                  const event = new Event("dispatchTextToSpeech");
                  event.data = activity?.text;
                  event.showMuteIcon = showMuteIcon;
                  event.hideMuteIcon = hideMuteIcon;
                  event.tts_gender = tts_gender;
                  window.dispatchEvent(event);    
                }
             
              }  
              if(activity?.type === 'welcome_message_set') {
                if(tts){
                  const welcomeTextMessages = activity?.set?.filter( a => a?.type === 'message');
                  welcomeTextMessages.map( a => {
                    const event = new Event("dispatchTextToSpeech");
                    event.data = a?.text;
                    event.showMuteIcon = showMuteIcon;
                    event.hideMuteIcon = hideMuteIcon;
                    event.tts_gender = tts_gender;
                    window.dispatchEvent(event);   
                  });
                 
                }
                activity?.set?.map(a => cb(a));     
              }   
              else{
                cb(activity); 
              }
                  
            }
         
          }
          
      }); 
}