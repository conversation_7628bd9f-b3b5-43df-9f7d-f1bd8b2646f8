import { socketServer } from "../constants";

const postinteractedWithBot = (data) => {
  return fetch(socketServer.concat("/open-user"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const accumalateMessage = (data) => {
  return fetch(socketServer.concat("/accumalate-message"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};



export { 
    postinteractedWithBot,
    accumalateMessage
};
