import { createPostJSON } from "../helper/customs";
import { accumalateMessage } from "./messaging.api";
const mockURL = "http://bot-designer-server.awdfaydwabfah9f5.uaenorth.azurecontainer.io:5003/api/ssc/chatbot";

const sendMessage = (data) => {
  // accumalateMessage({
  //   userId:data.user_id
  // });
  return fetch(`${mockURL}`, createPostJSON(data))
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

export { sendMessage };
