import { serverURL } from "../constants";
import { createGetJSON } from "../helper/customs";
const dest = "/zendeskplugin";
const zendesk = "/zendesk_websocket";
const getOneZendesk = async (bot_id) => {
  const res = await fetch(
    `${serverURL}${dest}?bot_id=${bot_id}`,
    createGetJSON()
  ).then((res) => res.json());
  console.log(res, "resgetonezendesk");
  return res;
};

const getWebSocketUrl = async (bot_id) => {
  const res = await fetch(
    `${serverURL}${zendesk}?bot_id=${bot_id}`,
    createGetJSON()
  ).then((res) => res.json());
  return res;
};
export { getOneZendesk, getWebSocketUrl };
