import { serverURL } from "../constants";
import {
  createGetJSON,
} from "../helper/customs";

const dest = "/trigger";



const getTriggersByName = (trigger_name) => {
    return fetch(
        serverURL.concat(dest, "s/name?trigger_name=", trigger_name),
        createGetJSON()
      )
        .then((response) => {
          return response.json();
        })
        .catch((err) => console.log(err));
};



export { 
    getTriggersByName
};
