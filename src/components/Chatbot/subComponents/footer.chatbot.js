import React, { useState, useEffect } from "react";
import { eventDispatcher, sttDispatcher } from "../../../Scripts";
// import { socket } from "../socketContext";
import { closeSession, sendClientMessage } from "../../../Apis/livechat.api";
import * as signalR from "@microsoft/signalr";

const ChatbotFooter = ({
  showTypingIndicator,
  pushToActivitiesHistory,
  setMenuInfo,
  botInfo,
  menuInfo,
  voiceLanguage,
  setVoiceLanguage,
  itemToAskAbout,
  isTtsActive,
  setIsTtsActive,
  voicePlugin,
  design,
  activitiesHistory,
  setIncomingActivity,
  conversationID,
  showHeader,
  livechat,
  bot_id,
  user_id,
  bot_name,
  deviceInfo,
  setDeviceInfo
}) => {
  const [isInputFocused, setIsInputFocused] = useState(false);
  const [userInput, setUserInput] = useState("");
  const [isVoiceActive, setIsVoiceActive] = useState(false);
  const [autoCompleteIndex, setAutoCompleteIndex] = useState(0);
  const [livepersonId, setLivepersonId] = useState(undefined);
  const [seatconversationId, setSeatconversation_id] = useState(undefined);

  useEffect(() => {
    console.log("socket start");
    // if(Boolean(deviceInfo)){
    //   const socketServer = "https://i2i-messaging.azurewebsites.net";
    //   var deviceInfoQuriesString = "";
    //   const keys = Object.keys(deviceInfo);
    //   for(var i =0; i<keys.length; i++){
    //     deviceInfoQuriesString = deviceInfoQuriesString.concat(`&${keys[i]}=${deviceInfo[keys[i]]}`);
    //   }
    //   const negotiationURL = `${socketServer}/api?userId=${user_id}&conversation_id=${conversationID}&channel=webchat&bot_id=${bot_id}&type=client${deviceInfoQuriesString}`;
    //   console.log("negotiationURL",negotiationURL)
    //   console.log("user_id", user_id);
    //   const _connection = new signalR.HubConnectionBuilder()
    //     .withUrl(negotiationURL)
    //     .build();
    //   _connection
    //     .start()
    //     .then(() => console.log("SHOULD BE WORKING"))
    //     .catch((err) => {});
  
    //     _connection.on("chatMessage", (message) => {
    //       console.log("chatMessage message", message);
    //       setIncomingActivity({
    //         from: "bot",
    //         type: "message",
    //         text: message,
    //         hideToolbar: true
    //       });
    //     });
    //     setDeviceInfo(undefined);
    // }    
  }, [deviceInfo]);

  const handleMessageFromLivePerson = (options) => {
    // if (options.liveperson_id && !livepersonId) {
    //   setLivepersonId(options.liveperson_id);
    // }
    // if (options.seatconversation_id && !seatconversationId) {
    //   setSeatconversation_id(options.seatconversation_id);
    // }
    // if (options.stop_livechat) {
    //   setLivechat(false);
    // }
    setIncomingActivity({ ...options });
  };

  console.log("showHeader", showHeader);
  return (
    <>
      <div
        className="searchat-chatbot-footer"
        style={{
          position: "fixed",
          bottom: showHeader ? "50px" : "0px",
          height: "60px",
          padding: "10px",
          zIndex: 2222,
          backgroundColor: "#eee",
          right: "0px",
          display: "flex",
          justifyContent: "center",
          alignItems: "center",
          background: design?.headerColor,
        }}
      >
        <div style={{ width: "100%", position: "relative" }}>
          <input
            list="activityHistory"
            id="input-user-message"
            placeholder={
              isVoiceActive
                ? voiceLanguage === "ar-EG"
                  ? "انا اسمعك"
                  : "I can hear you"
                : Boolean(design?.placeholder)
                ? design?.placeholder
                : "Type your message"
            }
            style={{
              border: `2px solid ${
                isInputFocused ? "#CD853F" : "rgb(221, 221, 221)"
              }`,
              boxShadow: isInputFocused ? `#CD853F 0px 4px 12px` : "none",
              borderRadius: "10px",
              outline: "none",
              width: "100%",
              boxSizing: "border-box",
              padding: "14px 70px 14px 14px",
              resize: "none",
              fontSize: "14px",
              fontWeight: 400,
              lineHeight: 1.14,
              height: "50px",
              color: "rgb(70, 70, 70) !important",
              paddingRight: "80px",
              paddingLeft: "40px",
            }}
            // onFocus={() => setIsInputFocused(true)}
            // onBlur={() => setIsInputFocused(false)}
            onChange={(event) => setUserInput(event.target.value)}
            autoComplete="off"
            value={userInput}
            onKeyDown={(event) => {
              const user_input_history = [
                ...new Set(activitiesHistory.filter((a) => a.from === "user")),
              ];
              // event = event || window.event;
              if (event.key === "ArrowDown" || event.key === "Down") {
                if (autoCompleteIndex !== user_input_history.length + 1) {
                  setUserInput(
                    user_input_history[autoCompleteIndex + 1]?.text || ""
                  );
                  setAutoCompleteIndex(autoCompleteIndex + 1);
                }
              }

              if (event.key === "ArrowUp" || event.key === "Up") {
                if (autoCompleteIndex) {
                  setUserInput(
                    user_input_history[autoCompleteIndex - 1]?.text || ""
                  );
                  setAutoCompleteIndex(autoCompleteIndex - 1);
                }
              }
            }}
            onKeyPress={(event) => {
              if (event.key === "Enter" && Boolean(userInput) && livechat) {
                sendClientMessage({
                  userId: user_id,
                  message: userInput,
                  conversation_id: conversationID,
                });
                pushToActivitiesHistory({
                  type: "message",
                  text: userInput,
                  from: "user",
                });

                setUserInput("");
                setMenuInfo(undefined);
                setTimeout(() => showTypingIndicator(), 500);
              } else if (event.key === "Enter" && Boolean(userInput)) {
                // setAutoCompleteIndex(
                //   [
                //     ...new Set(
                //       activitiesHistory.filter((a) => a.from === "user")
                //     ),
                //   ].length + 1
                // );
                eventDispatcher(
                  JSON.stringify({
                    user_input: Boolean(itemToAskAbout)
                      ? `${itemToAskAbout} ${userInput}`
                      : userInput,
                    bot_id: botInfo.bot_id,
                  }),

                  async (activities) => {
                    activities.map((a) => {
                      console.log("a", a);
                      setIncomingActivity(a);
                    });
                    var text = "";
                    const text_arr = activities.filter(
                      (a) => a.type === "message"
                    );
                    text_arr.forEach((a) => {
                      text = text.concat(` ${a.text}`);
                    });
                    const event = new Event("dispatchTextToSpeech");
                    text = text.replace(new RegExp("<[^>]*>", "g"), "");

                    event.data = text;
                    event.showMuteIcon = () => setIsTtsActive(true);
                    event.hideMuteIcon = () => setIsTtsActive(false);
                    event.tts_gender = voicePlugin?.tts_gender;
                    window.dispatchEvent(event);
                  },
                  conversationID,
                  botInfo.bot_id,
                  user_id
                );
                pushToActivitiesHistory({
                  type: "message",
                  text: userInput,
                  from: "user",
                });
                setUserInput("");
                setMenuInfo(undefined);
                showTypingIndicator();
              }
            }}
          ></input>
          {/iPhone|iPad|iPod/i.test(navigator.userAgent) ||
          (!activitiesHistory?.find((a) => a.text === userInput) &&
            userInput?.length) ? (
            <datalist id="activityHistory">
              {[
                ...new Set(
                  activitiesHistory
                    .filter((a) => a.from === "user")
                    .map((a) => a.text)
                ),
              ].map((a) => (
                <option value={a} />
              ))}
            </datalist>
          ) : null}

          <div className="flex__Flex-sc-1e2t5u1-0 message-input-styled__Actions-sc-4se7a7-3 kOkRBC koxcRa">
            {/* <div
              className="searchat-btn-div"
              style={{
                right: "20px",
                top: "12px",
              }}
              onClick={() => {
                if (Boolean(menuInfo)) {
                  setMenuInfo(undefined);
                } else {
                  setMenuInfo({
                    type: "menu",
                    menu_type: "main_menu",
                    rating: null,
                    notes: "",
                  });
                }
              }}
            >
              <i className="fas fa-ellipsis-v" id="mainmenuicon"></i>
            </div> */}

            <div
              className="searchat-btn-div"
              style={{ right: "50px", top: "12px" }}
              onClick={() => {
                setAutoCompleteIndex(
                  [
                    ...new Set(
                      activitiesHistory.filter((a) => a.from === "user")
                    ),
                  ].length + 1
                );
                eventDispatcher(
                  JSON.stringify({
                    user_input: Boolean(itemToAskAbout)
                      ? `${itemToAskAbout} ${userInput}`
                      : userInput,
                    bot_id: botInfo.bot_id,
                  }),

                  async (activities) => {
                    activities.map((a) => {
                      console.log("a", a);
                      setIncomingActivity(a);
                    });
                    var text = "";
                    const text_arr = activities.filter(
                      (a) => a.type === "message"
                    );
                    text_arr.forEach((a) => {
                      text = text.concat(` ${a.text}`);
                    });
                    const event = new Event("dispatchTextToSpeech");
                    text = text.replace(new RegExp("<[^>]*>", "g"), "");

                    event.data = text;
                    event.showMuteIcon = () => setIsTtsActive(true);
                    event.hideMuteIcon = () => setIsTtsActive(false);
                    event.tts_gender = voicePlugin?.tts_gender;
                    window.dispatchEvent(event);
                  },
                  conversationID,
                  botInfo.bot_id,
                  user_id
                );

                pushToActivitiesHistory({
                  type: "message",
                  text: userInput,
                  from: "user",
                });
                setUserInput("");
                setMenuInfo(undefined);
                showTypingIndicator();
              }}
            >
              <i className="fas fa-paper-plane"></i>
            </div>

            {!isTtsActive ? (
              <div
                className="searchat-btn-div"
                style={{ left: "10px", top: "12px" }}
                onClick={() => {
                  if (Boolean(userInput) && livechat) {
                    sendClientMessage({
                      userId: user_id,
                      message: userInput,
                      conversation_id: conversationID,
                    });

                    pushToActivitiesHistory({
                      type: "message",
                      text: userInput,
                      from: "user",
                    });
                  } else {
                    sttDispatcher(
                      true,
                      botInfo,
                      voiceLanguage,
                      (text) => {
                        if (voicePlugin?.show_stt_text) {
                          pushToActivitiesHistory({
                            type: "message",
                            text: text,
                            from: "user",
                          });
                        }
                        eventDispatcher(
                          JSON.stringify({
                            user_input: text,
                            bot_id: botInfo.bot_id,
                            is_voice: true,
                          }),
                          async (activities) => {
                            activities.forEach((a) => {
                              console.log("a", a);
                              setIncomingActivity(a);
                            });
                            var text = "";
                            const text_arr = activities.filter(
                              (a) => a.type === "message"
                            );
                            text_arr.forEach((a) => {
                              text = text.concat(` ${a.text}`);
                            });
                            const event = new Event("dispatchTextToSpeech");
                            text = text.replace(new RegExp("<[^>]*>", "g"), "");

                            event.data = text;
                            event.showMuteIcon = () => setIsTtsActive(true);
                            event.hideMuteIcon = () => setIsTtsActive(false);
                            event.tts_gender = voicePlugin?.tts_gender;
                            window.dispatchEvent(event);
                          },
                          conversationID,
                          botInfo.bot_id,
                          user_id
                        );
                        showTypingIndicator();
                      },
                      itemToAskAbout
                    );
                  }
                  setIsVoiceActive(true);
                  setTimeout(() => setIsVoiceActive(false), 5000);
                }}
              >
                {isVoiceActive && design?.voiceIcon ? (
                  <div className="sonar-emitter">
                    <i id="mic" className="fas fa-microphone"></i>
                    <div className="sonar-wave sonar-wave1">
                      <div className="sonar-wave sonar-wave2"></div>
                      <div className="sonar-wave sonar-wave3"></div>
                      <div className="sonar-wave sonar-wave4"></div>
                    </div>
                  </div>
                ) : voicePlugin?.stt && design?.voiceIcon ? (
                  <i id="mic" className="fas fa-microphone"></i>
                ) : null}
              </div>
            ) : (
              <div
                className="searchat-btn-div"
                style={{ left: "10px", top: "12px" }}
                onClick={() => {
                  setIsTtsActive(false);
                  const event = new Event("dispatchTextToSpeechStop");

                  window.dispatchEvent(event);
                }}
              >
                <i
                  className="fas fa-volume-mute"
                  id="searchat-bot-voice-btn"
                ></i>
              </div>
            )}
          </div>
        </div>
      </div>
      {showHeader ? (
        <div
          className="searchat-chatbot-branding"
          style={{
            position: "fixed",
            bottom: "10px",
            height: "40px",
            padding: "10px",
            backgroundColor: "white",
            right: "0px",
            display: "flex",
            justifyContent: "center",
            alignItems: "center",
            borderBottomRightRadius: "10px",
            borderBottomLeftRadius: "10px",
          }}
        >
          <div
            style={{
              position: "absolute",
              left: "10px",
              top: "30px",
              color: "white",
              fontSize: "24px",
              cursor: "pointer",
            }}
          >
            <div className="button r" id="button-6">
              <input
                type="checkbox"
                className="checkbox"
                checked={voiceLanguage === "en-US"}
                onChange={() =>
                  setVoiceLanguage(
                    voiceLanguage === "ar-EG" ? "en-US" : "ar-EG"
                  )
                }
              />
              <div className="knobs"></div>
              <div className="layer"></div>
            </div>
          </div>

          {design?.branding ? (
            <div>
              <img
                src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADYAAAA2CAYAAACMRWrdAAAAAXNSR0IArs4c6QAACLtJREFUaAXtmmuMVVcVgNfa59zzuO8rMwwDMy2kvBKEqpDGVuxLxVbTqqn0ocFMaOVhJS0ajT9J2tRGa2jUoENrY6rENETtn2rUKW2wSYVaCwWECBbk2c4M933vOfc89nLtOwwmk068dzh3QMPO7Dln77PPPutb+7HW2jMAV9NVDVzVwEQN0ABYtBpsAhATn7VbvuQO2v3gZO1H1kIqQPsjDdvsh9WAk7Vrtf6KAKM1kMhpqdtI067nsSLcCWGrAJO1u+xgtAnSYSZ5h9TE/UKDkXPQODWZsO3UX1Yw2gi50EjfRYa+SRiY1zSxb97PwW0HYLK2+mQPOl1P61JdYUa7mzSxHiQkAeUuOFc+GdV3L8uI0TdTXbJbv5ds/etgiCWI8Jrvum/y2vL+Z8GqjyR6fFO/T9r6RjDxg0R0HGQ4ZPnu2aigVD/TOhXp0XhvkDAfgFRiLSTsReDUA5T1lzXH24PbwY8SbNqmIn0tMStI2gOUttdD/+JFkO7VyAv/ji6vrf1OpKOlFDQtYPXN9hxvhjFAOWOdmL/iOuhZrINTc8jz/qSRvxdfhSDK0ZoWMAUVy1lrMGc8KOYu64e5H9PQrxMU3z1EDflnOFEbiRpK9dfRNUbfSszysuZDlIqtFbMXzMFFd2ngN4DePehIt/ZHv+S/YUXgZbyfYjo2FdVIBTnrIZGNbRB91/bhws9okJoNMLKf6PzJQ+TKV5M/rXZktBRoR8Do23ZfrDu+AXL6Izhndg/Ov1NAz00AhcNAZ/c50PB2mW7pr+zpsiPfmRT5VKQt9jVBPP4wZvQN0DMjjfNWAc76OEBjBOToW0C14b/xxj6ET0OxM0hjvUYGxqpHeDx7TWBpmzFrrIWZ2RRecwtg7838hJ31yiGC/KEqkNylw/m9nYRSfUcCxsGh1liSuk6z9A2Yiz3IUEnsu4mhbgfQeLY77LCX3iYIint04Q/hFihf8WB0KyvnQ7nFunKRMvpXYFYmibNXMNStgDELKKwC1A8z3JE6GvQWJOVp+mGyG7i6mSpjl/Fis3ShkBx7dLEqGVYd2Aa1VtbmJUWqtA5iXl9uMaZ5k8jq92N3MoG9y3lN3Q5odfPOwHbX5dE6PwSQP1iAarCHqvKfUJdADZ68DZZZmWa+lZKvzcy/lHPFdUKVOckGt3fCvPDly7pT3YMthDZTnooXoVL6egUFXfEEdi8E7L4R0MyyYBxWEUvuHWfhTwOYsSyQWAWCJTY5e5xDlZXk1KwmBaLqPAbh5+RzRcBvEYxCIH+tNWAE5rYWAUwJjLaA4YvMUkxqG/8DNR9w5koAq2sMSkkc8jwjVr/Zy6tZIlgSMclChxIwYAAG4s2EfxiA75Hrwef3qiWgSp2vJNGXZ4jkjlA2ntV63RO8PhX+f01tgx1aDUYgUsshHnsY0voXIGvGMTcXsOtGAJsBJHsWzfnFArLcYM4DiM3he1XmeSdDQHUvx+6bdSGXQy77HlD1PE87hgooJEceg1r4nF53d+hb3bOtrK1x4rbAjm4C89oZ6Q/ztPoG2NpnIaHZYCUYqI+FRhbqBPerFMqCXsg4tnDGtny17Y8/F/xpjTMPJAeaPGr8zMsDcBBNlVKA1WAf1RlqJNiJ251RfrGt1DLYcT7z60tnb5Ax7VGw9Ds58rWaMjZ4hPJHeTt/h1e7GiJOyFeVlV+j6vgrqPF9M6v6GIDBo6vxWuRpSEEDoDbM/ZzmTabkQcF/XVaDQbcS/sHYXmHa9lNLYMpO1WeklwaIXxWIN/B6qPLOVgW1uGu8N+drQEpo1rzK2ATiG52zwXUGX22msbQ4xDS+8jpU1CGfBHgMxS4jjTLUaLlOBX+3LAc/NmuF3eZWuGAMuHmbqSUw1afUZV2EYkj6crdiGE+kIHgAmqNxobJZpxpZWnNwtDjqUmgfRYGfg1g6A4LBJDfwWSEV9oPz5wiGKxVZ9H+HFe8Zs1h6HbeCc6G7KV1aAlMHmLS6ehT64J22Dsf6x2TyM11LNA1XgmlaIHK8oNhwuzV2sxTUMHv7tSLkvReg7v9MHy4dwB81LdyUgMZfaglMNZ7qCVL5qVRXQuAneV5+AmIZAzDJ5o1tnIpY8qNEo/URWQp+RTUaPHCqdGxFRGcfLYONa6KdK9s73dOMlRK1L6KZ6AYtheD5vJ0XGGqEodwzsuz9Aire8wZUFBTv+dGkjoJ58IGFQhdfAtteCmZGKLHJ5ZEq5hnKORaWwkHp1F+wwTnbquFtFbtjYOpQ1LPwbrKMVSKRjAHbLVkZBSwVpSx4/wiL/g/8sv/b1HedPO+ZbAOiTR0Ba/qRWe1mNuIDkImnSTcRqhxXlkohlfx9ouxtjZ0vvGg/BbyDdCZ1BMzvySwDKzYg0tYCMC2EOtu6cjlgG/Ua26jvmWcKQ1EfkE5UT+RgtCU+27P1ezAR+xTZhsCGQ1SquVgOXgmK7hP2kcpfovj710SQieVIwWgz2L5mfRoM/ctsnC3wA6KqW+Y47PdU9p62H6vs7cR6mgilypGBKbfLz6WXseu0Bi3RDyHHIHVvFCrhb9iZ3WY+Vj7wfgJ0qi4SMN7S0F2S7eczj/vA1PicjSSHHKfZk9hJpfAn1pPKQ57eFAlYfgukUoa4jT3+e1BDIRv0L3DCHbLhPGs/6ZycXqSxr10ymPIuglh2OZnaAGjYw+H8YdGQzzcqzi+TTzjvXQ4o9c1LAmtOwSDTj7b+gBBwPUe9+8ENBuvDwYuZrbUpxVFRKeKSwOA7mawWF3eQcnJDejNww+3Vs/mXZm67eLgWlZxt9zNlMP43BjOwYYUEcS+E8m0+oBm0g8Ir8W1tBTZtC9zqC1MC43XF4WVqHpH4PB9jnBKBN6jLyhvsyHJIfGWkKYFBIznDj4lbCGURPdquH6kcnA5voh2VtQ3WnIImLmDzG/BoPWc8XjzO3oQ6mrqiUttghThYyRDD0A1eSny/9t50uUgd15oKSdT/P3X8Q1c/cFUD/x8a+DcWMsuQEzmwYQAAAABJRU5ErkJggg=="
                alt="powered-icon"
                style={{ height: "18px" }}
              />
              &nbsp;by{" "}
              <a
                href="https://infotointell.com"
                target="_blank"
                style={{ marginLeft: "5px" }}
              >
                infotointell
              </a>
            </div>
          ) : null}
        </div>
      ) : null}
    </>
  );
};

export default ChatbotFooter;
