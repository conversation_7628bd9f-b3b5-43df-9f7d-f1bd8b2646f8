<?xml version="1.0" encoding="UTF-8"?>
<configuration>
  <system.webServer>
    <caching enabled="false" enableKernelCache="false"/>

    <handlers>
      <clear />
      <add 
        name="StaticFile" 
        path="*" verb="*" 
        modules="StaticFileModule,DefaultDocumentModule,DirectoryListingModule" 
        resourceType="Either" 
        requireAccess="Read" />
    </handlers>

    <staticContent>
      <mimeMap fileExtension=".json" mimeType="application/json" />
      <mimeMap fileExtension=".*" mimeType="application/octet-stream" />
      <mimeMap fileExtension=".webmanifest" mimeType="application/manifest+json" />
    </staticContent>

    <modules runAllManagedModulesForAllRequests="true"/>
    
    <httpProtocol>
      <customHeaders>
        <add name="Cache-Control" value="no-cache, no-store, must-revalidate, pre-check=0, post-check=0, max-age=0, s-maxage=0" />
        <add name="Pragma" value="no-cache" />
        <add name="Expires" value="0" />
      </customHeaders>
    </httpProtocol>

  </system.webServer>
</configuration>
