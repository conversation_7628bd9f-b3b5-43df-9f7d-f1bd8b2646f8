<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="utf-8" />
  <meta name="robots" content="noindex" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0,user-scalable=0" />
  <meta name="theme-color" content="#000000" />

  <meta name="robots" content="noindex" />


  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <link crossorigin="use-credentials" rel="manifest" href="./manifest.json" />

  <link rel="preconnect" href="https://fonts.googleapis.com" />
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
  <link
    href="https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&family=Tajawal:wght@200;300;400;500;700;800;900&display=swap"
    rel="stylesheet" />

  <script src="https://kit.fontawesome.com/7061c7631a.js" crossorigin="anonymous"></script>

  <!--
      manifest.json provides metadata used when your web app is installed on a
      user's mobile device or desktop. See https://developers.google.com/web/fundamentals/web-app-manifest/
    -->
  <!-- <script src="https://i2ibasicvoice.azurewebsites.net/tts/js/i2i_BV_ES.js?key=rn3lvWzH"></script> -->

  <!--
      Notice the use of %PUBLIC_URL% in the tags above.
      It will be replaced with the URL of the `public` folder during the build.
      Only files inside the `public` folder can be referenced from the HTML.

      Unlike "/favicon.ico" or "favicon.ico", "%PUBLIC_URL%/favicon.ico" will
      work correctly both with client-side routing and a non-root public URL.
      Learn how to configure a non-root public URL by running `npm run build`.
    -->

</head>

<body>
  <noscript>You need to enable JavaScript to run this app.</noscript>
  <div id="root"></div>
  <!--
      This HTML file is a template.
      If you open it directly in the browser, you will see an empty page.

      You can add webfonts, meta tags, or analytics to this file.
      The build step will place the bundled scripts into the <body> tag.

      To begin the development, run `npm start` or `yarn start`.
      To create a production bundle, use `npm run build` or `yarn build`.
    -->
  <!-- <script id="ze-snippet" src="https://static.zdassets.com/ekr/snippet.js?key=431db543-5236-4f62-a2c1-9fc16c3ed0d8"> </script> -->

  <script>
    const _0x5ce8 = [
      "then",
      "193288gSdagf",
      "852730GTXyqF",
      "POST",
      "transcript",
      "1044288vFzNDq",
      "onstart",
      "5113bKTgPQ",
      "none",
      "3kjHVfD",
      "getElementById",
      "result",
      "292070ZIFjOH",
      "#status",
      "application/json",
      "1zkLZsm",
      "STT",
      "style",
      "results",
      "188549FBNfih",
      "172224INgAtn",
      "N/A",
      "lang",
      "152wDstWs",
      "json",
      "querySelector",
      "stringify",
      "display",
      "log",
      "block",
    ];
    (function (_0x437834, _0x26e83c) {
      const _0x11c782 = _0x504a;
      while (!![]) {
        try {
          const _0x267083 =
            -parseInt(_0x11c782(0x1f5)) * -parseInt(_0x11c782(0x1e7)) +
            parseInt(_0x11c782(0x1f3)) +
            -parseInt(_0x11c782(0x1f0)) +
            parseInt(_0x11c782(0x1e4)) * -parseInt(_0x11c782(0x1f7)) +
            parseInt(_0x11c782(0x1ef)) +
            -parseInt(_0x11c782(0x1fa)) +
            -parseInt(_0x11c782(0x1e3)) * -parseInt(_0x11c782(0x1df));
          if (_0x267083 === _0x26e83c) break;
          else _0x437834["push"](_0x437834["shift"]());
        } catch (_0x32eea0) {
          _0x437834["push"](_0x437834["shift"]());
        }
      }
    })(_0x5ce8, 0x84485);
    const Speech_T_text = async (_0x189f96, _0x46bd9e) => {
      const _0x2ce1b6 = _0x504a;
      if (!window["webkitSpeechRecognition"])
        console[_0x2ce1b6(0x1ec)](_0x2ce1b6(0x1e5));
      else {
        const _0x6008e3 = new Promise((_0x10ecb7, _0x58ba75) => {
          const _0x2a6e71 = _0x2ce1b6,
            _0x568ab3 = new webkitSpeechRecognition();
          (_0x568ab3[_0x2a6e71(0x1e6)] = _0x46bd9e),
            (_0x568ab3[_0x2a6e71(0x1f4)] = (_0x5c5438) => {
              const _0x159e9a = _0x2a6e71;
              var _0x58214f = document["getElementById"]("status");
              _0x58214f &&
                (document["querySelector"](_0x159e9a(0x1fb))[
                  _0x159e9a(0x1e1)
                ][_0x159e9a(0x1eb)] = _0x159e9a(0x1ed));
            }),
            (_0x568ab3["onend"] = () => {
              const _0x2daba1 = _0x2a6e71;
              var _0x5404ed = document[_0x2daba1(0x1f8)]("status");
              _0x5404ed &&
                (document[_0x2daba1(0x1e9)](_0x2daba1(0x1fb))[
                  _0x2daba1(0x1e1)
                ][_0x2daba1(0x1eb)] = _0x2daba1(0x1f6));
            }),
            _0x568ab3["start"](),
            _0x568ab3["addEventListener"](
              _0x2a6e71(0x1f9),
              async (_0x42239b) => {
                const _0x287525 = _0x2a6e71;
                if (_0x42239b[_0x287525(0x1e2)]["length"] > 0x0) {
                  _0x10ecb7(_0x42239b["results"][0x0][0x0][_0x287525(0x1f2)]);
                  const _0x2bdb5d = JSON[_0x287525(0x1ea)]({
                    project_name: _0x189f96,
                    user_id: _0x287525(0x1e0),
                    user_question: "",
                    bot_answer:
                      _0x42239b[_0x287525(0x1e2)][0x0][0x0]["transcript"],
                    similarity: 0x64,
                  });
                } else
                  _0x58ba75("try\x20again,\x20something\x20went\x20wrong...");
              }
            );
        });
        return await _0x6008e3;
      }
    };
    function _0x504a(_0x4ddf32, _0x3b09be) {
      return (
        (_0x504a = function (_0x5ce8e4, _0x504afe) {
          _0x5ce8e4 = _0x5ce8e4 - 0x1de;
          let _0x118b6e = _0x5ce8[_0x5ce8e4];
          return _0x118b6e;
        }),
        _0x504a(_0x4ddf32, _0x3b09be)
      );
    }
    window.Speech_T_text = Speech_T_text;
  </script>
  <script src="https://i2ibasicvoice.azurewebsites.net/tts/SR/i2i_BV11_2.js"></script>

  <script>
    const IsJsonString = (str) => {
      try {
        JSON.parse(str);
      } catch (e) {
        return false;
      }
      return true;
    };

    window.addEventListener(
      "dispatchTextToSpeech",
      ({ data, showMuteIcon, hideMuteIcon, tts_gender }) => {
        const ar_regex = new RegExp(
          "[\u0600-\u06ff]|[\u0750-\u077f]|[\ufb50-\ufc3f]|[\ufe70-\ufefc]"
        );
        if (data) {
          console.log(
            "data",
            ar_regex.test(data),
            showMuteIcon,
            hideMuteIcon,
            tts_gender
          );
          if (Boolean(data)) {
            if (ar_regex.test(data)) {
              i2i_BV.speak(
                data,
                "Arabic Male",
                {
                  rate: 1,
                  pitch: 1,
                  onstart: function () {
                    showMuteIcon();
                  },
                  onend: function () {
                    hideMuteIcon();
                  },
                }
              );
            } else {
              i2i_BV.speak(
                data,
                "UK English Male",
                {
                  rate: 1,
                  pitch: 1,
                  onstart: function () {
                    showMuteIcon();
                  },
                  onend: function () {
                    hideMuteIcon();
                  },
                }
              );
            }
          }
        }
      }
    );

    window.addEventListener("dispatchTextToSpeechStop", () => {
      i2i_BV.cancel();
    });
  </script>
    <!-- <script>

      window.addEventListener("message", (e) => {
        const data = JSON.parse(e.data)
           const socketServer = "https://i2i-messaging.azurewebsites.net/api";
const postinteractedWithBot = (data) => {

  return fetch(socketServer.concat("/open-icon"), {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  })
    .then((response) => {
      return response.json();
    })
    .catch((err) => console.log(err));
};

const setParent = (data) => {

return fetch(socketServer.concat("/set-parent"), {
  method: "POST",
  headers: {
    "Content-Type": "application/json",
  },
  body: JSON.stringify(data),
})
  .then((response) => {
    return response.json();
  })
  .catch((err) => console.log(err));
};

// if(data.type === "open_widget"){
//   postinteractedWithBot({
//       userId : data.userId
//     });
// }
   

// if(data.type === "set_parent"){
//   if(Boolean(data.parentUserId)){
//       setParent({
//         parent_id:data.parentUserId,
//         parent_conversation_id: data.parentConversationID,
//         child_conversation_id: data.conversationID,
//         child_id: data.userId,
//         bot_id: data.bot_id
//       })
//     }
// }
   
  
    
  
      });
    </script> -->

<!-- <script>
  window.addEventListener("beforeunload", async function (e) {
    const data = {
      userId: localStorage.getItem("outside"),
      type: "client",
    };
    await fetch("https://i2i-messaging.azurewebsites.net/api/closeuser", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });
    return;

    // e = e || window.event;

    // // // For IE and Firefox prior to version 4
    // if (e) {
    //   e.returnValue = "Sure?";
    // }

    // // // For Safari
    // return "Sure?";
  });
</script> -->
</body>

</html>